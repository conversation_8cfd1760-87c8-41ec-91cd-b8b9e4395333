package logicFish

import (
	"context"
	daoKeep "spotsrv/internal/dao/dao_keep"
	daoKeepnet "spotsrv/internal/dao/dao_keepnet"
	logicNotify "spotsrv/internal/logic/logic_notify"
	modelKeepnet "spotsrv/internal/model/model_keepnet"

	commonPB "git.keepfancy.xyz/back-end/fancy-common/pkg/cpb/common"
	"git.keepfancy.xyz/back-end/fancy-common/pkg/repo/logx"
	"git.keepfancy.xyz/back-end/frameworks/kit/safego"
)

// TODO 暂时定义 后续使用玩家身上携带的鱼护装备属性进行校验
const (
	FISH_WEIGHT_LIMIT    = 20000  // 鱼最大重量限制 20KG
	KEEPNEI_WEIGHT_LIMIT = 100000 // 鱼护最大容量 100KG
)

type FishEntryOptInterface interface {
	// PlayerFishEntryOpt 玩家入护操作
	PlayerFishEntryOpt(ctx context.Context, playerId uint64, fishInstance string) (int32, commonPB.ErrCode)
	// OnFishEntry 入户时的额外操作，比如事件推送
	OnFishEntry(ctx context.Context, playerId uint64, fishDetail *modelKeepnet.FishDetailInfo) error
}

func FishEntryOpt(ctx context.Context, playerId uint64, fishInstance string, optType commonPB.FISH_ENTRY_OPT_TYPE) (int32, commonPB.ErrCode) {
	entry := logx.NewLogEntry(ctx)

	// 先判断是否有需要入护的鱼
	fishDetail, err := daoKeep.GetPlayerHookFish(ctx, playerId)
	// 异常处理
	if err != nil || fishDetail == nil {
		// 查询玩家鱼护数据
		keepnet, err := daoKeepnet.GetPlayerKeepnetFish(ctx, playerId)
		if err != nil {
			entry.Errorf("query keepnet info error playerId:%d, optType:%+v instance:%s, err:%v", playerId, optType, fishInstance, err)
			return 0, commonPB.ErrCode_ERR_FAIL
		}

		playerKeepnet := modelKeepnet.NewFishKeepnetFromRedisHash(ctx, keepnet)
		if inFish, ok := playerKeepnet.FishList[fishInstance]; ok && inFish != nil {
			entry.Warnf("fish already in keepnet, playerId:%d, instance:%s", playerId, fishInstance)
			return 0, commonPB.ErrCode_ERR_SPOT_FISH_IN_KEEPENT
		} else {
			return 0, commonPB.ErrCode_ERR_SPOT_FISH_HAS_OUT
		}
	}

	var entryOpt FishEntryOptInterface
	switch optType {
	case commonPB.FISH_ENTRY_OPT_TYPE_FEOT_KEEP:
		entryOpt = new(FishKeep)
	case commonPB.FISH_ENTRY_OPT_TYPE_FEOT_RELEASE:
		entryOpt = new(FishRelease)
	default:
		entryOpt = nil
	}

	if entryOpt != nil {
		return entryOpt.PlayerFishEntryOpt(ctx, playerId, fishInstance)
	}

	return 0, commonPB.ErrCode_ERR_BAD_PARAM
}

// 保存钓上来的鱼
type FishKeep struct {
}

// PlayerFishEntryOpt 保存钓上来的鱼
func (f *FishKeep) PlayerFishEntryOpt(ctx context.Context, playerId uint64, fishInstance string) (int32, commonPB.ErrCode) {
	entry := logx.NewLogEntry(ctx)
	// 先判断是否有需要入护的鱼
	fishDetail, err := daoKeep.GetPlayerHookFish(ctx, playerId)
	if err != nil || fishDetail == nil {
		entry.Errorf("query hook fish error, playerId:%d, instance:%s, err:%v", playerId, fishInstance, err)
		return 0, commonPB.ErrCode_ERR_FAIL
	}
	// 入护标记
	fishDetail.OptType = commonPB.FISH_ENTRY_OPT_TYPE_FEOT_KEEP

	// 鱼重限制
	if fishDetail.FishInfo.Weight > FISH_WEIGHT_LIMIT {
		entry.Warnf("fish weight too big, playerId:%d, instance:%s, weight:%d", playerId, fishInstance, fishDetail.FishInfo.Weight)
		return 0, commonPB.ErrCode_ERR_SPOT_FISH_WEIGHT_LIMIT
	}

	// 查询玩家鱼护数据
	keepnet, err := daoKeepnet.GetPlayerKeepnetFish(ctx, playerId)
	if err != nil {
		entry.Errorf("query keepnet info error playerId:%d, instance:%s, err:%v", playerId, fishInstance, err)
		return 0, commonPB.ErrCode_ERR_FAIL
	}

	playerKeepnet := modelKeepnet.NewFishKeepnetFromRedisHash(ctx, keepnet)

	// 鱼护总重量限制
	if playerKeepnet.TotalWeight >= KEEPNEI_WEIGHT_LIMIT {
		entry.Warnf("fish keepnet weight too big, playerId:%d, instance:%s, weight:%d", playerId, fishInstance, playerKeepnet.TotalWeight)
		return 0, commonPB.ErrCode_ERR_SPOT_KEEPENT_FULL
	}

	err = playerKeepnet.AddFish(ctx, fishDetail)
	if err != nil {
		entry.Errorf("add fish error, playerId:%d, instance:%s, keepnet:%v, detail:%v, err:%v", playerId, fishInstance, playerKeepnet, fishDetail, err)
		return 0, commonPB.ErrCode_ERR_SPOT_ADD_FISH_EXIST
	}

	// 更新玩家鱼护数据
	err = daoKeepnet.AddPlayerKeepnetFish(ctx, playerId, fishDetail)
	if err != nil {
		entry.Errorf("update keepnet info error playerId:%d, instance:%s, err:%v", playerId, fishInstance, err)
		return 0, commonPB.ErrCode_ERR_FAIL
	}

	// 删除还未入护的鱼
	err = daoKeep.DelPlayerHookFish(ctx, playerId)
	if err != nil {
		entry.Errorf("after keep hook fish del , playerId:%d, instance:%s, err:%v", playerId, fishInstance, err)
	}

	entry.Infof("add hook fish, playerId:%d, instance:%s, keepnet:%v", playerId, fishInstance, playerKeepnet)

	return playerKeepnet.TotalWeight, commonPB.ErrCode_ERR_SUCCESS
}

// 释放钓上来的鱼
type FishRelease struct {
}

// FishEntryRelease 释放钓上来的鱼，放进鱼护标记起来
func (f *FishRelease) PlayerFishEntryOpt(ctx context.Context, playerId uint64, fishInstance string) (int32, commonPB.ErrCode) {
	entry := logx.NewLogEntry(ctx)

	// 先判断是否有需要入护的鱼
	fishDetail, err := daoKeep.GetPlayerHookFish(ctx, playerId)
	if err != nil || fishDetail == nil {
		entry.Errorf("query hook fish error, playerId:%d, instance:%s, err:%v", playerId, fishInstance, err)
		return 0, commonPB.ErrCode_ERR_FAIL
	}
	// 放生标记
	fishDetail.OptType = commonPB.FISH_ENTRY_OPT_TYPE_FEOT_RELEASE

	// 查询玩家鱼护数据
	keepnet, err := daoKeepnet.GetPlayerKeepnetFish(ctx, playerId)
	if err != nil {
		entry.Errorf("query keepnet info error playerId:%d, instance:%s, err:%v", playerId, fishInstance, err)
		return 0, commonPB.ErrCode_ERR_FAIL
	}

	playerKeepnet := modelKeepnet.NewFishKeepnetFromRedisHash(ctx, keepnet)

	err = playerKeepnet.AddFish(ctx, fishDetail)
	if err != nil {
		entry.Errorf("add fish error, playerId:%d, instance:%s, keepnet:%v, detail:%v, err:%v", playerId, fishInstance, playerKeepnet, fishDetail, err)
		return 0, commonPB.ErrCode_ERR_SPOT_ADD_FISH_EXIST
	}

	// 更新玩家鱼护数据
	err = daoKeepnet.AddPlayerKeepnetFish(ctx, playerId, fishDetail)
	if err != nil {
		entry.Errorf("update keepnet info error playerId:%d, instance:%s, err:%v", playerId, fishInstance, err)
		return 0, commonPB.ErrCode_ERR_FAIL
	}

	// 释放鱼
	err = daoKeep.DelPlayerHookFish(ctx, playerId)
	if err != nil {
		entry.Errorf("release hook fish, playerId:%d, instance:%s, err:%v", playerId, fishInstance, err)
		return 0, commonPB.ErrCode_ERR_FAIL
	}

	// 钓鱼广播: 释放鱼
	eventInfo := commonPB.FishingEventInfo{
		EventType: commonPB.FISHING_EVENT_TYPE_FET_RELEASE_KEPPENT,
		PlayerId:  playerId,
		IntData: map[int32]int64{
			int32(commonPB.EVENT_INT_KEY_EIK_FISH_ID): fishDetail.FishInfo.FishId,
		},
	}

	safego.Go(func() {
		logicNotify.SendFishingEventBsNtf(ctx, playerId, &eventInfo)
	})

	return 0, commonPB.ErrCode_ERR_SUCCESS
}
